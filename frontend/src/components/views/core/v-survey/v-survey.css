#ready-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  min-height: 100vh;
  max-width: none; /* Override any inherited max-width constraints */
}

#survey-header-bar {
  position: fixed;
  top: 60px;
  left: 0;
  right: 0;
  background: var(--color__bg);
  border-bottom: var(--border);
  padding: 1em 0em;
  z-index: 8;
  box-sizing: border-box;
  width: 1024px;
  margin: 0 auto;
}

#ready-container__main-content {
  margin-left: 210px;
  margin-top: 100px;
  width: calc(100% - 210px);
  box-sizing: border-box;
}

/* Ensure all pattern components take full width */
p-survey-overview,
p-survey-analytics,
p-survey-responses,
p-survey-export {
  display: block;
  width: 100%;
}

.content {
  width: 100%;
}

.centered {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 50vh;
}

#error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 50vh;
}

#error-container article {
  text-align: center;
  max-width: 400px;
}
